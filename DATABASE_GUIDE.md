# 🗄️ Database Management Guide

## نحوه استفاده از دیتابیس

### 📊 ساختار دیتابیس

دیتابیس شامل 3 جدول اصلی است:

#### 1. جدول Users (کاربران)
- `user_id` - شناسه کاربر تلگرام
- `username` - نام کاربری
- `first_name` - نام
- `last_name` - نام خانوادگی
- `language` - زبان انتخابی
- `wallet_balance` - موجودی کیف پول
- `is_premium` - وضعیت پریمیوم
- `is_banned` - وضعیت مسدودی
- `join_date` - تاریخ عضویت
- `last_activity` - آخرین فعالیت

#### 2. جدول Transactions (تراکنش‌ها)
- `id` - شناسه تراکنش
- `user_id` - شن<PERSON><PERSON>ه کاربر
- `amount` - مبلغ
- `transaction_type` - نوع تراکنش
- `description` - توضیحات
- `timestamp` - زمان تراکنش

#### 3. جدو<PERSON> User Settings (تنظیمات کاربر)
- `user_id` - شناسه کاربر
- `notifications` - اعلان‌ها
- `auto_delete` - حذف خودکار
- `theme` - تم

## 🎛️ Admin Panel

### راه‌اندازی Admin Panel:
```bash
python admin_panel.py
```

### قابلیت‌های Admin Panel:

#### 1️⃣ List All Users
- نمایش لیست همه کاربران
- نمایش موجودی، وضعیت پریمیوم و مسدودی

#### 2️⃣ User Information
- نمایش اطلاعات کامل یک کاربر
- تاریخ عضویت و آخرین فعالیت

#### 3️⃣ Add Money to Wallet
- اضافه کردن پول به کیف پول کاربر
- ثبت تراکنش در تاریخچه

#### 4️⃣ Remove Money from Wallet
- کم کردن پول از کیف پول کاربر
- ثبت تراکنش منفی

#### 5️⃣ Set Premium Status
- فعال/غیرفعال کردن وضعیت پریمیوم

#### 6️⃣ Ban User
- مسدود کردن کاربر

#### 7️⃣ Unban User
- رفع مسدودیت کاربر

#### 8️⃣ Delete User
- حذف کامل کاربر از دیتابیس
- نیاز به تایید با تایپ "DELETE"

#### 9️⃣ User Transactions
- نمایش تاریخچه تراکنش‌های کاربر

#### 🔍 Search User
- جستجو کاربر با نام کاربری یا نام

## 💻 استفاده برنامه‌نویسی

### Import کردن دیتابیس:
```python
from database import db
```

### مثال‌های کاربردی:

#### اضافه کردن کاربر:
```python
db.add_user(
    user_id=123456789,
    username="john_doe",
    first_name="John",
    last_name="Doe",
    language="en"
)
```

#### دریافت اطلاعات کاربر:
```python
user = db.get_user(123456789)
if user:
    print(f"Balance: ${user['wallet_balance']}")
```

#### اضافه کردن پول:
```python
# اضافه کردن $10
db.update_wallet_balance(123456789, 10.0, "admin_credit", "Bonus money")
```

#### کم کردن پول:
```python
# کم کردن $5
db.update_wallet_balance(123456789, -5.0, "purchase", "File download")
```

#### تنظیم پریمیوم:
```python
# فعال کردن پریمیوم
db.set_user_premium(123456789, True)

# غیرفعال کردن پریمیوم
db.set_user_premium(123456789, False)
```

#### مسدود کردن کاربر:
```python
# مسدود کردن
db.ban_user(123456789, True)

# رفع مسدودیت
db.ban_user(123456789, False)
```

#### دریافت تراکنش‌ها:
```python
transactions = db.get_user_transactions(123456789, limit=5)
for tx in transactions:
    print(f"{tx['amount']} - {tx['description']}")
```

## 📁 فایل‌های دیتابیس

### مکان فایل دیتابیس:
- `bot_database.db` - فایل اصلی دیتابیس SQLite

### پشتیبان‌گیری:
```bash
# کپی کردن فایل دیتابیس
cp bot_database.db backup_database.db
```

### بازیابی:
```bash
# بازگردانی از پشتیبان
cp backup_database.db bot_database.db
```

## 🔧 نکات مهم

### امنیت:
- فایل دیتابیس را در مکان امن نگهداری کنید
- به صورت منظم پشتیبان‌گیری کنید
- دسترسی Admin Panel را محدود کنید

### عملکرد:
- دیتابیس SQLite برای ربات‌های کوچک تا متوسط مناسب است
- برای ربات‌های بزرگ از PostgreSQL یا MySQL استفاده کنید

### مانیتورینگ:
- به صورت منظم لاگ‌ها را بررسی کنید
- تراکنش‌های مشکوک را پیگیری کنید
- موجودی کیف پول‌ها را کنترل کنید

## 🚀 مثال کامل

```python
from database import db

# اضافه کردن کاربر جدید
user_id = 123456789
db.add_user(user_id, "john_doe", "John", "Doe", "en")

# اضافه کردن پول
db.update_wallet_balance(user_id, 50.0, "welcome_bonus", "Welcome gift")

# فعال کردن پریمیوم
db.set_user_premium(user_id, True)

# چک کردن اطلاعات
user = db.get_user(user_id)
print(f"User: {user['first_name']}")
print(f"Balance: ${user['wallet_balance']}")
print(f"Premium: {user['is_premium']}")
```

این سیستم دیتابیس کامل و آماده استفاده است! 🎉
