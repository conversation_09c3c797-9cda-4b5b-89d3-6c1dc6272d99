import sqlite3
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any

logger = logging.getLogger(__name__)

class Database:
    def __init__(self, db_path: str = "bot_database.db"):
        """Initialize database connection and create tables."""
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """Get database connection."""
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """Create database tables if they don't exist."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Users table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    user_id INTEGER PRIMARY KEY,
                    username TEXT,
                    first_name TEXT,
                    last_name TEXT,
                    language TEXT DEFAULT 'en',
                    wallet_balance REAL DEFAULT 0.0,
                    is_premium BOOLEAN DEFAULT 0,
                    is_banned BOOLEAN DEFAULT 0,
                    join_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Transactions table for wallet history
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    amount REAL,
                    transaction_type TEXT,
                    description TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (user_id)
                )
            ''')
            
            # User settings table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_settings (
                    user_id INTEGER PRIMARY KEY,
                    notifications BOOLEAN DEFAULT 1,
                    auto_delete BOOLEAN DEFAULT 0,
                    theme TEXT DEFAULT 'glass',
                    FOREIGN KEY (user_id) REFERENCES users (user_id)
                )
            ''')
            
            conn.commit()
            logger.info("Database initialized successfully")
    
    def add_user(self, user_id: int, username: str = None, first_name: str = None, 
                 last_name: str = None, language: str = 'en') -> bool:
        """Add a new user or update existing user info."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO users 
                    (user_id, username, first_name, last_name, language, last_activity)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (user_id, username, first_name, last_name, language, datetime.now()))
                
                # Create default settings for new user
                cursor.execute('''
                    INSERT OR IGNORE INTO user_settings (user_id)
                    VALUES (?)
                ''', (user_id,))
                
                conn.commit()
                logger.info(f"User {user_id} added/updated successfully")
                return True
        except Exception as e:
            logger.error(f"Error adding user {user_id}: {e}")
            return False
    
    def get_user(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user information."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT user_id, username, first_name, last_name, language, 
                           wallet_balance, is_premium, is_banned, join_date, last_activity
                    FROM users WHERE user_id = ?
                ''', (user_id,))
                
                row = cursor.fetchone()
                if row:
                    return {
                        'user_id': row[0],
                        'username': row[1],
                        'first_name': row[2],
                        'last_name': row[3],
                        'language': row[4],
                        'wallet_balance': row[5],
                        'is_premium': row[6],
                        'is_banned': row[7],
                        'join_date': row[8],
                        'last_activity': row[9]
                    }
                return None
        except Exception as e:
            logger.error(f"Error getting user {user_id}: {e}")
            return None
    
    def update_user_language(self, user_id: int, language: str) -> bool:
        """Update user's language preference."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE users SET language = ?, last_activity = ?
                    WHERE user_id = ?
                ''', (language, datetime.now(), user_id))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error updating language for user {user_id}: {e}")
            return False
    
    def update_wallet_balance(self, user_id: int, amount: float, 
                            transaction_type: str = "admin", description: str = "") -> bool:
        """Update user's wallet balance and log transaction."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Get current balance
                cursor.execute('SELECT wallet_balance FROM users WHERE user_id = ?', (user_id,))
                result = cursor.fetchone()
                if not result:
                    return False
                
                current_balance = result[0]
                new_balance = current_balance + amount
                
                # Update balance
                cursor.execute('''
                    UPDATE users SET wallet_balance = ?, last_activity = ?
                    WHERE user_id = ?
                ''', (new_balance, datetime.now(), user_id))
                
                # Log transaction
                cursor.execute('''
                    INSERT INTO transactions (user_id, amount, transaction_type, description)
                    VALUES (?, ?, ?, ?)
                ''', (user_id, amount, transaction_type, description))
                
                conn.commit()
                logger.info(f"Wallet updated for user {user_id}: {amount} ({transaction_type})")
                return True
        except Exception as e:
            logger.error(f"Error updating wallet for user {user_id}: {e}")
            return False
    
    def set_user_premium(self, user_id: int, is_premium: bool) -> bool:
        """Set user premium status."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE users SET is_premium = ?, last_activity = ?
                    WHERE user_id = ?
                ''', (is_premium, datetime.now(), user_id))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error setting premium status for user {user_id}: {e}")
            return False
    
    def ban_user(self, user_id: int, is_banned: bool = True) -> bool:
        """Ban or unban a user."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE users SET is_banned = ?, last_activity = ?
                    WHERE user_id = ?
                ''', (is_banned, datetime.now(), user_id))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error banning user {user_id}: {e}")
            return False
    
    def delete_user(self, user_id: int) -> bool:
        """Delete a user completely."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Delete from all tables
                cursor.execute('DELETE FROM transactions WHERE user_id = ?', (user_id,))
                cursor.execute('DELETE FROM user_settings WHERE user_id = ?', (user_id,))
                cursor.execute('DELETE FROM users WHERE user_id = ?', (user_id,))
                
                conn.commit()
                logger.info(f"User {user_id} deleted successfully")
                return True
        except Exception as e:
            logger.error(f"Error deleting user {user_id}: {e}")
            return False
    
    def get_all_users(self) -> List[Dict[str, Any]]:
        """Get all users."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT user_id, username, first_name, wallet_balance, 
                           is_premium, is_banned, join_date
                    FROM users ORDER BY join_date DESC
                ''')
                
                users = []
                for row in cursor.fetchall():
                    users.append({
                        'user_id': row[0],
                        'username': row[1],
                        'first_name': row[2],
                        'wallet_balance': row[3],
                        'is_premium': row[4],
                        'is_banned': row[5],
                        'join_date': row[6]
                    })
                return users
        except Exception as e:
            logger.error(f"Error getting all users: {e}")
            return []
    
    def get_user_transactions(self, user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """Get user's transaction history."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT amount, transaction_type, description, timestamp
                    FROM transactions WHERE user_id = ?
                    ORDER BY timestamp DESC LIMIT ?
                ''', (user_id, limit))
                
                transactions = []
                for row in cursor.fetchall():
                    transactions.append({
                        'amount': row[0],
                        'type': row[1],
                        'description': row[2],
                        'timestamp': row[3]
                    })
                return transactions
        except Exception as e:
            logger.error(f"Error getting transactions for user {user_id}: {e}")
            return []

# Global database instance
db = Database()
