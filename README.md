# 🌟 Glass Telegram Bot

یک ربات تلگرام شیشه‌ای با قابلیت انتخاب زبان (فارسی/انگلیسی)

## ویژگی‌ها

- 🎨 طراحی شیشه‌ای و مدرن
- 🌍 پشتیبانی از دو زبان فارسی و انگلیسی
- 🇮🇷 دکمه انتخاب زبان با پرچم ایران
- 🇺🇸 دکمه انتخاب زبان با پرچم آمریکا
- ⚡ پاسخ‌دهی سریع با Inline Keyboards

## نصب و راه‌اندازی

### 1. نصب کتابخانه‌ها
```bash
pip install -r requirements.txt
```

### 2. دریافت توکن ربات
1. به [@BotFather](https://t.me/BotFather) در تلگرام پیام دهید
2. دستور `/newbot` را ارسال کنید
3. نام ربات و username آن را انتخاب کنید
4. توکن دریافتی را کپی کنید

### 3. تنظیم توکن
فایل `bot.py` را باز کنید و در خط زیر توکن خود را قرار دهید:
```python
BOT_TOKEN = "YOUR_BOT_TOKEN"  # توکن خود را اینجا قرار دهید
```

### 4. اجرای ربات
```bash
python bot.py
```

## نحوه استفاده

1. ربات را در تلگرام پیدا کنید
2. دستور `/start` را ارسال کنید
3. زبان مورد نظر خود را انتخاب کنید:
   - 🇮🇷 Persian (فارسی)
   - 🇺🇸 English (انگلیسی)

## ساختار پروژه

```
TelegramProjects/
├── bot.py              # فایل اصلی ربات
├── requirements.txt    # کتابخانه‌های مورد نیاز
└── README.md          # راهنمای استفاده
```

## مرحله بعدی

این نسخه اولیه ربات شما آماده است. برای اضافه کردن ویژگی‌های بیشتر، می‌توانید:

- منوهای بیشتر اضافه کنید
- قابلیت‌های جدید بسازید
- طراحی شیشه‌ای را بهبود دهید
- دیتابیس برای ذخیره اطلاعات کاربران اضافه کنید

## پشتیبانی

اگر سوالی دارید یا نیاز به کمک دارید، لطفاً پیام دهید!
