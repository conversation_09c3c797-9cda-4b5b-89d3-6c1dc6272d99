import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes

# Enable logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Store user language preferences
user_languages = {}

# Language texts
TEXTS = {
    'en': {
        'welcome': '🌟 Welcome to Glass Bot! 🌟\n\nPlease Set Language:',
        'language_set': '✅ Language set to English!\n\nWelcome to your glass-style bot experience!',
        'choose_language': 'Please choose your language:'
    },
    'fa': {
        'welcome': '🌟 به ربات شیشه‌ای خوش آمدید! 🌟\n\nلطفاً زبان را انتخاب کنید:',
        'language_set': '✅ زبان به فارسی تنظیم شد!\n\nبه تجربه ربات شیشه‌ای خوش آمدید!',
        'choose_language': 'لطفاً زبان خود را انتخاب کنید:'
    }
}

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Send a message when the command /start is issued."""
    user_id = update.effective_user.id
    
    # Create glass-style inline keyboard with language options
    keyboard = [
        [
            InlineKeyboardButton("🇮🇷 Persian", callback_data='lang_fa'),
            InlineKeyboardButton("🇺🇸 English", callback_data='lang_en')
        ]
    ]
    
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    # Send welcome message with language selection
    welcome_text = "🌟 Welcome to Glass Bot! 🌟\n\nPlease Set Language:"
    
    await update.message.reply_text(
        welcome_text,
        reply_markup=reply_markup,
        parse_mode='HTML'
    )

async def language_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle language selection callback."""
    query = update.callback_query
    await query.answer()
    
    user_id = query.from_user.id
    language = query.data.split('_')[1]  # Extract language code from callback_data
    
    # Store user's language preference
    user_languages[user_id] = language
    
    # Get appropriate text based on selected language
    text = TEXTS[language]['language_set']
    
    # Edit the message to show confirmation
    await query.edit_message_text(
        text=text,
        parse_mode='HTML'
    )
    
    # You can add more functionality here after language is set
    logger.info(f"User {user_id} selected language: {language}")

async def get_user_language(user_id: int) -> str:
    """Get user's preferred language, default to English."""
    return user_languages.get(user_id, 'en')

def main() -> None:
    """Start the bot."""
    # Replace 'YOUR_BOT_TOKEN' with your actual bot token from @BotFather
    BOT_TOKEN = "YOUR_BOT_TOKEN"
    
    # Create the Application
    application = Application.builder().token(BOT_TOKEN).build()
    
    # Add handlers
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CallbackQueryHandler(language_callback, pattern='^lang_'))
    
    # Run the bot until the user presses Ctrl-C
    print("🤖 Glass Bot is starting...")
    print("Press Ctrl+C to stop the bot")
    
    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == '__main__':
    main()
