import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes, MessageHandler, filters
from database import db
from config import ADMIN_IDS, BOT_TOKEN, ADMIN_LOGIN_COMMAND, ADMIN_LOGOUT_COMMAND

# Enable logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Store admin sessions
admin_sessions = set()

# Helper functions for admin
def is_admin(user_id: int) -> bool:
    """Check if user is admin."""
    return user_id in ADMIN_IDS

def is_admin_logged_in(user_id: int) -> bool:
    """Check if admin is logged in."""
    return user_id in admin_sessions

def admin_login(user_id: int) -> bool:
    """Login admin."""
    if is_admin(user_id):
        admin_sessions.add(user_id)
        return True
    return False

def admin_logout(user_id: int) -> bool:
    """Logout admin."""
    if user_id in admin_sessions:
        admin_sessions.remove(user_id)
        return True
    return False

# Language texts
TEXTS = {
    'en': {
        'welcome': '🌟 Welcome to Glass Bot! 🌟\n\nPlease Set Language:',
        'language_set': '✅ Welcome to Null Core Bot buddy!',
        'choose_language': 'Please choose your language:',
        'main_menu': '✅ I am a professional all-in-one bot with super speed!\n\n☑️ Just choose from the menu below',
        'file_to_link': 'File To Link',
        'uploader': 'Uploader',
        'file_to_link_selected': 'File To Link selected!\n\nThis feature will be available soon...!',
        'uploader_selected': 'Uploader selected!\n\nThis feature will be available soon...!',
        'admin_panel': 'Admin Panel',
        'admin_welcome': 'Welcome to NullCore Bot Admin Panel!\nChoose an option from the menu below:',
        'admin_all_users': 'All Users',
        'admin_user_info': 'User Info',
        'admin_add_money': 'Add Money',
        'admin_remove_money': 'Remove Money',
        'admin_set_premium': 'Set Premium',
        'admin_ban_user': 'Ban User',
        'admin_statistics': 'Statistics',
        'admin_search_user': 'Search User',
        'admin_logout': 'Logout',
        'admin_manage_admins': 'Manage Admins',
        'admin_add_admin': 'Add Admin',
        'admin_remove_admin': 'Remove Admin',
        'admin_list_admins': 'List Admins',
        'admin_broadcast': 'Broadcast Message',
        'admin_send_message': 'Send Message',
        'admin_system_settings': 'System Settings',
        'admin_backup': 'Database Backup',
        'admin_logs': 'View Logs',
        'admin_back': 'Back',
        'language_selected': 'Language selected!',
        'admin_start_instruction': 'Use /start to access admin panel.'
    },
    'fa': {
        'welcome': '🌟 به ربات شیشه‌ای خوش آمدید! 🌟\n\nلطفاً زبان را انتخاب کنید:',
        'language_set': '✅ به بات نال کور خوش اومدی رفیق!',
        'choose_language': 'لطفاً زبان خود را انتخاب کنید:',
        'main_menu': '✅ من یک ربات همه کاره هستم اونم به صورت حرفه ای و با سرعت فوق العاده !\n\n☑️ کافیه فقط از منوی زیر انتخاب کنی',
        'file_to_link': 'فایل تو لینک',
        'uploader': 'آپلودر',
        'file_to_link_selected': 'فایل تو لینک انتخاب شد!\n\nاین ویژگی به زودی در دسترس قرار خواهد گرفت...!',
        'uploader_selected': 'آپلودر انتخاب شد!\n\nاین ویژگی به زودی در دسترس قرار خواهد گرفت...!',
        'admin_panel': 'پنل مدیریت',
        'admin_welcome': 'به پنل مدیریت بات نال کور خوش اومدی!\nیکی از گزینه‌های زیر رو انتخاب کن:',
        'admin_all_users': 'همه کاربران',
        'admin_user_info': 'اطلاعات کاربر',
        'admin_add_money': 'اضافه کردن پول',
        'admin_remove_money': 'کم کردن پول',
        'admin_set_premium': 'تنظیم پریمیوم',
        'admin_ban_user': 'مسدود کردن کاربر',
        'admin_statistics': 'آمار',
        'admin_search_user': 'جستجوی کاربر',
        'admin_logout': 'خروج',
        'admin_manage_admins': 'مدیریت ادمین‌ها',
        'admin_add_admin': 'اضافه کردن ادمین',
        'admin_remove_admin': 'حذف کردن ادمین',
        'admin_list_admins': 'لیست ادمین‌ها',
        'admin_broadcast': 'پیام همگانی',
        'admin_send_message': 'ارسال پیام',
        'admin_system_settings': 'تنظیمات سیستم',
        'admin_backup': 'بک‌آپ دیتابیس',
        'admin_logs': 'مشاهده لاگ‌ها',
        'admin_back': 'بازگشت',
        'language_selected': 'زبان انتخاب شد!',
        'admin_start_instruction': 'برای دسترسی به پنل ادمین از /start استفاده کن.'
    },
    'ru': {
        'welcome': '🌟 Добро пожаловать в Glass Bot! 🌟\n\nПожалуйста, выберите язык:',
        'language_set': '✅ Добро пожаловать в Null Core Bot, друг!',
        'choose_language': 'Пожалуйста, выберите ваш язык:',
        'main_menu': '✅ Я профессиональный многофункциональный бот с супер скоростью!\n\n☑️ Просто выберите из меню ниже',
        'file_to_link': 'Файл в ссылку',
        'uploader': 'Загрузчик',
        'file_to_link_selected': 'Файл в ссылку выбран!\n\nЭта функция скоро будет доступна...!',
        'uploader_selected': 'Загрузчик выбран!\n\nЭта функция скоро будет доступна...!',
        'admin_panel': 'Панель администратора',
        'admin_welcome': 'Добро пожаловать в панель администратора NullCore Bot!\nВыберите опцию из меню ниже:',
        'admin_all_users': 'Все пользователи',
        'admin_user_info': 'Информация о пользователе',
        'admin_add_money': 'Добавить деньги',
        'admin_remove_money': 'Убрать деньги',
        'admin_set_premium': 'Установить премиум',
        'admin_ban_user': 'Заблокировать пользователя',
        'admin_statistics': 'Статистика',
        'admin_search_user': 'Поиск пользователя',
        'admin_logout': 'Выход',
        'admin_manage_admins': 'Управление админами',
        'admin_add_admin': 'Добавить админа',
        'admin_remove_admin': 'Удалить админа',
        'admin_list_admins': 'Список админов',
        'admin_broadcast': 'Рассылка',
        'admin_send_message': 'Отправить сообщение',
        'admin_system_settings': 'Системные настройки',
        'admin_backup': 'Резервная копия БД',
        'admin_logs': 'Просмотр логов',
        'admin_back': 'Назад',
        'language_selected': 'Язык выбран!',
        'admin_start_instruction': 'Используйте /start для доступа к панели администратора.'
    },
    'de': {
        'welcome': '🌟 Willkommen bei Glass Bot! 🌟\n\nBitte wählen Sie eine Sprache:',
        'language_set': '✅ Willkommen bei Null Core Bot, Kumpel!',
        'choose_language': 'Bitte wählen Sie Ihre Sprache:',
        'main_menu': '✅ Ich bin ein professioneller Allzweck-Bot mit super Geschwindigkeit!\n\n☑️ Wählen Sie einfach aus dem Menü unten',
        'file_to_link': 'Datei zu Link',
        'uploader': 'Uploader',
        'file_to_link_selected': 'Datei zu Link ausgewählt!\n\nDiese Funktion wird bald verfügbar sein...!',
        'uploader_selected': 'Uploader ausgewählt!\n\nDiese Funktion wird bald verfügbar sein...!',
        'admin_panel': 'Admin-Panel',
        'admin_welcome': 'Willkommen im NullCore Bot Admin-Panel!\nWählen Sie eine Option aus dem Menü unten:',
        'admin_all_users': 'Alle Benutzer',
        'admin_user_info': 'Benutzerinfo',
        'admin_add_money': 'Geld hinzufügen',
        'admin_remove_money': 'Geld entfernen',
        'admin_set_premium': 'Premium setzen',
        'admin_ban_user': 'Benutzer sperren',
        'admin_statistics': 'Statistiken',
        'admin_search_user': 'Benutzer suchen',
        'admin_logout': 'Abmelden',
        'admin_manage_admins': 'Admins verwalten',
        'admin_add_admin': 'Admin hinzufügen',
        'admin_remove_admin': 'Admin entfernen',
        'admin_list_admins': 'Admin-Liste',
        'admin_broadcast': 'Rundnachricht',
        'admin_send_message': 'Nachricht senden',
        'admin_system_settings': 'Systemeinstellungen',
        'admin_backup': 'DB-Backup',
        'admin_logs': 'Logs anzeigen',
        'admin_back': 'Zurück',
        'language_selected': 'Sprache ausgewählt!',
        'admin_start_instruction': 'Verwenden Sie /start für den Zugang zum Admin-Panel.'
    },
    'fr': {
        'welcome': '🌟 Bienvenue sur Glass Bot! 🌟\n\nVeuillez choisir une langue:',
        'language_set': '✅ Bienvenue sur Null Core Bot, mon ami!',
        'choose_language': 'Veuillez choisir votre langue:',
        'main_menu': '✅ Je suis un bot professionnel polyvalent avec une super vitesse!\n\n☑️ Choisissez simplement dans le menu ci-dessous',
        'file_to_link': 'Fichier vers lien',
        'uploader': 'Téléchargeur',
        'file_to_link_selected': 'Fichier vers lien sélectionné!\n\nCette fonctionnalité sera bientôt disponible...!',
        'uploader_selected': 'Téléchargeur sélectionné!\n\nCette fonctionnalité sera bientôt disponible...!',
        'admin_panel': 'Panneau d\'administration',
        'admin_welcome': 'Bienvenue dans le panneau d\'administration de NullCore Bot!\nChoisissez une option dans le menu ci-dessous:',
        'admin_all_users': 'Tous les utilisateurs',
        'admin_user_info': 'Info utilisateur',
        'admin_add_money': 'Ajouter de l\'argent',
        'admin_remove_money': 'Retirer de l\'argent',
        'admin_set_premium': 'Définir premium',
        'admin_ban_user': 'Bannir utilisateur',
        'admin_statistics': 'Statistiques',
        'admin_search_user': 'Rechercher utilisateur',
        'admin_logout': 'Déconnexion',
        'admin_manage_admins': 'Gérer les admins',
        'admin_add_admin': 'Ajouter admin',
        'admin_remove_admin': 'Supprimer admin',
        'admin_list_admins': 'Liste des admins',
        'admin_broadcast': 'Message diffusé',
        'admin_send_message': 'Envoyer message',
        'admin_system_settings': 'Paramètres système',
        'admin_backup': 'Sauvegarde BD',
        'admin_logs': 'Voir les logs',
        'admin_back': 'Retour',
        'language_selected': 'Langue sélectionnée!',
        'admin_start_instruction': 'Utilisez /start pour accéder au panneau d\'administration.'
    },
    'es': {
        'welcome': '🌟 ¡Bienvenido a Glass Bot! 🌟\n\nPor favor selecciona un idioma:',
        'language_set': '✅ ¡Bienvenido a Null Core Bot, amigo!',
        'choose_language': 'Por favor elige tu idioma:',
        'main_menu': '✅ ¡Soy un bot profesional multipropósito con súper velocidad!\n\n☑️ Solo elige del menú de abajo',
        'file_to_link': 'Archivo a enlace',
        'uploader': 'Subidor',
        'file_to_link_selected': '¡Archivo a enlace seleccionado!\n\n¡Esta función estará disponible pronto...!',
        'uploader_selected': '¡Subidor seleccionado!\n\n¡Esta función estará disponible pronto...!',
        'admin_panel': 'Panel de administración',
        'admin_welcome': '¡Bienvenido al panel de administración de NullCore Bot!\nElige una opción del menú de abajo:',
        'admin_all_users': 'Todos los usuarios',
        'admin_user_info': 'Info de usuario',
        'admin_add_money': 'Agregar dinero',
        'admin_remove_money': 'Quitar dinero',
        'admin_set_premium': 'Establecer premium',
        'admin_ban_user': 'Banear usuario',
        'admin_statistics': 'Estadísticas',
        'admin_search_user': 'Buscar usuario',
        'admin_logout': 'Cerrar sesión',
        'admin_manage_admins': 'Gestionar admins',
        'admin_add_admin': 'Añadir admin',
        'admin_remove_admin': 'Eliminar admin',
        'admin_list_admins': 'Lista de admins',
        'admin_broadcast': 'Mensaje masivo',
        'admin_send_message': 'Enviar mensaje',
        'admin_system_settings': 'Configuración del sistema',
        'admin_backup': 'Copia de seguridad BD',
        'admin_logs': 'Ver registros',
        'admin_back': 'Atrás',
        'language_selected': '¡Idioma seleccionado!',
        'admin_start_instruction': 'Usa /start para acceder al panel de administración.'
    },
    'tr': {
        'welcome': '🌟 Glass Bot\'a hoş geldiniz! 🌟\n\nLütfen bir dil seçin:',
        'language_set': '✅ Null Core Bot\'a hoş geldin dostum!',
        'choose_language': 'Lütfen dilinizi seçin:',
        'main_menu': '✅ Ben süper hızlı profesyonel çok amaçlı bir botum!\n\n☑️ Sadece aşağıdaki menüden seçin',
        'file_to_link': 'Dosya bağlantısı',
        'uploader': 'Yükleyici',
        'file_to_link_selected': 'Dosya bağlantısı seçildi!\n\nBu özellik yakında kullanılabilir olacak...!',
        'uploader_selected': 'Yükleyici seçildi!\n\nBu özellik yakında kullanılabilir olacak...!',
        'admin_panel': 'Yönetici Paneli',
        'admin_welcome': 'NullCore Bot Yönetici Paneline hoş geldiniz!\nAşağıdaki menüden bir seçenek seçin:',
        'admin_all_users': 'Tüm kullanıcılar',
        'admin_user_info': 'Kullanıcı bilgisi',
        'admin_add_money': 'Para ekle',
        'admin_remove_money': 'Para çıkar',
        'admin_set_premium': 'Premium ayarla',
        'admin_ban_user': 'Kullanıcıyı yasakla',
        'admin_statistics': 'İstatistikler',
        'admin_search_user': 'Kullanıcı ara',
        'admin_logout': 'Çıkış',
        'admin_manage_admins': 'Yöneticileri yönet',
        'admin_add_admin': 'Yönetici ekle',
        'admin_remove_admin': 'Yönetici kaldır',
        'admin_list_admins': 'Yönetici listesi',
        'admin_broadcast': 'Toplu mesaj',
        'admin_send_message': 'Mesaj gönder',
        'admin_system_settings': 'Sistem ayarları',
        'admin_backup': 'Veritabanı yedeği',
        'admin_logs': 'Günlükleri görüntüle',
        'admin_back': 'Geri',
        'language_selected': 'Dil seçildi!',
        'admin_start_instruction': 'Yönetici paneline erişmek için /start kullanın.'
    },
    'zh': {
        'welcome': '🌟 欢迎使用Glass Bot! 🌟\n\n请选择语言:',
        'language_set': '✅ 欢迎使用Null Core Bot，朋友!',
        'choose_language': '请选择您的语言:',
        'main_menu': '✅ 我是一个专业的多功能超高速机器人!\n\n☑️ 只需从下面的菜单中选择',
        'file_to_link': '文件转链接',
        'uploader': '上传器',
        'file_to_link_selected': '文件转链接已选择!\n\n此功能即将推出...!',
        'uploader_selected': '上传器已选择!\n\n此功能即将推出...!',
        'admin_panel': '管理面板',
        'admin_welcome': '欢迎使用NullCore Bot管理面板!\n请从下面的菜单中选择一个选项:',
        'admin_all_users': '所有用户',
        'admin_user_info': '用户信息',
        'admin_add_money': '添加金钱',
        'admin_remove_money': '移除金钱',
        'admin_set_premium': '设置高级版',
        'admin_ban_user': '封禁用户',
        'admin_statistics': '统计数据',
        'admin_search_user': '搜索用户',
        'admin_logout': '登出',
        'admin_manage_admins': '管理管理员',
        'admin_add_admin': '添加管理员',
        'admin_remove_admin': '移除管理员',
        'admin_list_admins': '管理员列表',
        'admin_broadcast': '群发消息',
        'admin_send_message': '发送消息',
        'admin_system_settings': '系统设置',
        'admin_backup': '数据库备份',
        'admin_logs': '查看日志',
        'admin_back': '返回',
        'language_selected': '语言已选择!',
        'admin_start_instruction': '使用 /start 访问管理面板。'
    }
}

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Send a message when the command /start is issued."""
    user = update.effective_user
    username = user.first_name if user.first_name else user.username if user.username else "Friend"

    # Check if admin is logged in
    if is_admin_logged_in(user.id):
        await show_admin_language_selection(update)
        return

    # Add user to database
    db.add_user(
        user_id=user.id,
        username=user.username,
        first_name=user.first_name,
        last_name=user.last_name
    )

    # Create glass-style inline keyboard with language options
    keyboard = [
        [
            InlineKeyboardButton("🇮🇷 Persian", callback_data='lang_fa'),
            InlineKeyboardButton("🇺🇸 English", callback_data='lang_en')
        ],
        [
            InlineKeyboardButton("🇷🇺 Russia", callback_data='lang_ru'),
            InlineKeyboardButton("🇩🇪 Germany", callback_data='lang_de')
        ],
        [
            InlineKeyboardButton("🇫🇷 French", callback_data='lang_fr'),
            InlineKeyboardButton("🇪🇸 Spanish", callback_data='lang_es')
        ],
        [
            InlineKeyboardButton("🇹🇷 Turkey", callback_data='lang_tr'),
            InlineKeyboardButton("🇨🇳 Chinese", callback_data='lang_zh')
        ]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    # Send welcome message with language selection
    welcome_text = f"✋🏻Hello {username}\n\n🌟 Welcome To NullCore Bot! 🌟\n\nPlease Set Language:"

    await update.message.reply_text(
        welcome_text,
        reply_markup=reply_markup,
        parse_mode='HTML'
    )

async def language_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle language selection callback."""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    user = query.from_user
    username = user.first_name if user.first_name else user.username if user.username else "Friend"
    language = query.data.split('_')[1]  # Extract language code from callback_data

    # Store user's language preference in database
    db.update_user_language(user_id, language)

    # Create greeting based on language
    greetings = {
        'fa': f"✋🏻سلام {username}\n\n",
        'en': f"✋🏻Hello {username}\n\n",
        'ru': f"✋🏻Привет {username}\n\n",
        'de': f"✋🏻Hallo {username}\n\n",
        'fr': f"✋🏻Salut {username}\n\n",
        'es': f"✋🏻Hola {username}\n\n",
        'tr': f"✋🏻Merhaba {username}\n\n",
        'zh': f"✋🏻你好 {username}\n\n"
    }
    greeting = greetings.get(language, f"✋🏻Hello {username}\n\n")

    # Get appropriate text based on selected language
    welcome_text = greeting + TEXTS[language]['language_set']

    # Edit the message to show confirmation
    await query.edit_message_text(
        text=welcome_text,
        parse_mode='HTML'
    )

    # Send main menu after a short delay
    await show_main_menu(query, language)

    logger.info(f"User {user_id} selected language: {language}")

async def show_main_menu(query, language: str) -> None:
    """Show the main menu with glass-style buttons."""
    # Create main menu keyboard based on language
    keyboard = [
        [
            InlineKeyboardButton(TEXTS[language]['file_to_link'], callback_data='file_to_link'),
            InlineKeyboardButton(TEXTS[language]['uploader'], callback_data='uploader')
        ]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    # Send main menu message
    menu_text = TEXTS[language]['main_menu']

    await query.message.reply_text(
        text=menu_text,
        reply_markup=reply_markup,
        parse_mode='HTML'
    )

async def menu_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle main menu button callbacks."""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    language = await get_user_language(user_id)

    if query.data == 'file_to_link':
        await query.edit_message_text(
            text=f"🔗 {TEXTS[language]['file_to_link_selected']}",
            parse_mode='HTML'
        )
    elif query.data == 'uploader':
        await query.edit_message_text(
            text=f"📤 {TEXTS[language]['uploader_selected']}",
            parse_mode='HTML'
        )

# Admin Panel Functions
async def show_admin_panel(update: Update) -> None:
    """Show admin panel main menu."""
    user_id = update.effective_user.id
    user_lang = await get_user_language(user_id)
    texts = TEXTS.get(user_lang, TEXTS['en'])

    keyboard = [
        [
            InlineKeyboardButton(f"👥 {texts['admin_all_users']}", callback_data='admin_users'),
            InlineKeyboardButton(f"👤 {texts['admin_user_info']}", callback_data='admin_user_info')
        ],
        [
            InlineKeyboardButton(f"💰 {texts['admin_add_money']}", callback_data='admin_add_money'),
            InlineKeyboardButton(f"💸 {texts['admin_remove_money']}", callback_data='admin_remove_money')
        ],
        [
            InlineKeyboardButton(f"⭐ {texts['admin_set_premium']}", callback_data='admin_premium'),
            InlineKeyboardButton(f"🚫 {texts['admin_ban_user']}", callback_data='admin_ban')
        ],
        [
            InlineKeyboardButton(f"🔧 {texts['admin_manage_admins']}", callback_data='admin_manage_admins'),
            InlineKeyboardButton(f"📢 {texts['admin_broadcast']}", callback_data='admin_broadcast')
        ],
        [
            InlineKeyboardButton(f"📊 {texts['admin_statistics']}", callback_data='admin_stats'),
            InlineKeyboardButton(f"🔍 {texts['admin_search_user']}", callback_data='admin_search')
        ],
        [
            InlineKeyboardButton(f"⚙️ {texts['admin_system_settings']}", callback_data='admin_settings'),
            InlineKeyboardButton(f"🚪 {texts['admin_logout']}", callback_data='admin_logout')
        ]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    admin_text = f"🔧 **{texts['admin_panel']}**\n\n" \
                f"{texts['admin_welcome']}"

    if update.message:
        await update.message.reply_text(
            admin_text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    else:
        await update.callback_query.edit_message_text(
            admin_text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )

async def handle_admin_login(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle admin login command."""
    user_id = update.effective_user.id

    if not is_admin(user_id):
        await update.message.reply_text("❌ Access denied!")
        return

    if admin_login(user_id):
        logger.info(f"Admin {user_id} logged in")
        # Show success message and instruction to use /start
        user_lang = await get_user_language(user_id)
        if user_lang == 'fa':
            login_text = "✅ **ورود موفقیت‌آمیز!**\n\nبرای دسترسی به پنل ادمین از /start استفاده کن."
        else:
            login_text = "✅ **Login successful!**\n\nUse /start to access admin panel."

        await update.message.reply_text(
            login_text,
            parse_mode='Markdown'
        )
    else:
        await update.message.reply_text("❌ Login failed!")

async def show_admin_language_selection(update: Update) -> None:
    """Show language selection for admin after login"""
    keyboard = [
        [
            InlineKeyboardButton("🇮🇷 فارسی", callback_data="admin_lang_fa"),
            InlineKeyboardButton("🇺🇸 English", callback_data="admin_lang_en")
        ],
        [
            InlineKeyboardButton("🇷🇺 Русский", callback_data="admin_lang_ru"),
            InlineKeyboardButton("🇩🇪 Deutsch", callback_data="admin_lang_de")
        ],
        [
            InlineKeyboardButton("🇫🇷 Français", callback_data="admin_lang_fr"),
            InlineKeyboardButton("🇪🇸 Español", callback_data="admin_lang_es")
        ],
        [
            InlineKeyboardButton("🇹🇷 Türkçe", callback_data="admin_lang_tr"),
            InlineKeyboardButton("🇨🇳 中文", callback_data="admin_lang_zh")
        ]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    welcome_text = (
        "🔧 **Admin Panel Language Selection**\n\n"
        "Please select your preferred language for the admin panel:\n\n"
        "🇮🇷 فارسی | 🇺🇸 English | 🇷🇺 Русский | 🇩🇪 Deutsch\n"
        "🇫🇷 Français | 🇪🇸 Español | 🇹🇷 Türkçe | 🇨🇳 中文"
    )

    await update.message.reply_text(
        welcome_text,
        reply_markup=reply_markup,
        parse_mode='Markdown'
    )

async def handle_admin_logout(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle admin logout command."""
    user_id = update.effective_user.id
    user_lang = await get_user_language(user_id)

    if admin_logout(user_id):
        if user_lang == 'fa':
            logout_text = "✅ **ادمین با موفقیت خارج شد!**\n\nبرای دسترسی عادی از /start استفاده کن."
        else:
            logout_text = "✅ **Admin logged out successfully!**\n\nUse /start for normal bot access."

        await update.message.reply_text(
            logout_text,
            parse_mode='Markdown'
        )
        logger.info(f"Admin {user_id} logged out")
    else:
        not_logged_text = "❌ شما وارد نشده‌اید!" if user_lang == 'fa' else "❌ You are not logged in!"
        await update.message.reply_text(not_logged_text)

async def show_admin_manage_panel(update: Update) -> None:
    """Show admin management panel."""
    user_id = update.effective_user.id
    user_lang = await get_user_language(user_id)
    texts = TEXTS.get(user_lang, TEXTS['en'])

    keyboard = [
        [
            InlineKeyboardButton(f"➕ {texts['admin_add_admin']}", callback_data='admin_add_admin'),
            InlineKeyboardButton(f"➖ {texts['admin_remove_admin']}", callback_data='admin_remove_admin')
        ],
        [
            InlineKeyboardButton(f"📋 {texts['admin_list_admins']}", callback_data='admin_list_admins')
        ],
        [
            InlineKeyboardButton(f"🔙 {texts['admin_back']}", callback_data='admin_back_main')
        ]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    admin_text = f"🔧 **{texts['admin_manage_admins']}**\n\n" \
                f"انتخاب کنید که چه کاری انجام دهید:" if user_lang == 'fa' else f"Choose what you want to do:"

    await update.callback_query.edit_message_text(
        admin_text,
        reply_markup=reply_markup,
        parse_mode='Markdown'
    )

async def show_broadcast_panel(update: Update) -> None:
    """Show broadcast message panel."""
    user_id = update.effective_user.id
    user_lang = await get_user_language(user_id)
    texts = TEXTS.get(user_lang, TEXTS['en'])

    keyboard = [
        [
            InlineKeyboardButton(f"📢 {texts['admin_broadcast']}", callback_data='admin_send_broadcast'),
            InlineKeyboardButton(f"💬 {texts['admin_send_message']}", callback_data='admin_send_to_user')
        ],
        [
            InlineKeyboardButton(f"🔙 {texts['admin_back']}", callback_data='admin_back_main')
        ]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    broadcast_text = f"📢 **{texts['admin_broadcast']}**\n\n" \
                    f"نوع پیام را انتخاب کنید:" if user_lang == 'fa' else f"Choose message type:"

    await update.callback_query.edit_message_text(
        broadcast_text,
        reply_markup=reply_markup,
        parse_mode='Markdown'
    )

async def show_system_settings_panel(update: Update) -> None:
    """Show system settings panel."""
    user_id = update.effective_user.id
    user_lang = await get_user_language(user_id)
    texts = TEXTS.get(user_lang, TEXTS['en'])

    keyboard = [
        [
            InlineKeyboardButton(f"💾 {texts['admin_backup']}", callback_data='admin_backup_db'),
            InlineKeyboardButton(f"📋 {texts['admin_logs']}", callback_data='admin_view_logs')
        ],
        [
            InlineKeyboardButton(f"🔙 {texts['admin_back']}", callback_data='admin_back_main')
        ]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    settings_text = f"⚙️ **{texts['admin_system_settings']}**\n\n" \
                   f"تنظیمات سیستم را انتخاب کنید:" if user_lang == 'fa' else f"Choose system settings:"

    await update.callback_query.edit_message_text(
        settings_text,
        reply_markup=reply_markup,
        parse_mode='Markdown'
    )

async def admin_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle admin panel callbacks."""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id

    # Handle admin language selection
    if query.data.startswith('admin_lang_'):
        if not is_admin_logged_in(user_id):
            await query.edit_message_text("❌ Access denied! Please login first.")
            return

        lang_code = query.data.split('_')[-1]  # Extract language code

        # Update user language in database
        db.update_user_language(user_id, lang_code)

        # Show admin panel directly after language selection
        await show_admin_panel(update)
        return

    user_lang = await get_user_language(user_id)
    texts = TEXTS.get(user_lang, TEXTS['en'])

    if not is_admin_logged_in(user_id):
        await query.edit_message_text("❌ Access denied! Please login first.")
        return

    if query.data == 'admin_users':
        await show_all_users(query, user_lang)
    elif query.data == 'admin_stats':
        await show_statistics(query, user_lang)
    elif query.data == 'admin_logout':
        admin_logout(user_id)
        logout_text = "✅ **خروج موفقیت‌آمیز!**\n\nبرای دسترسی عادی از /start استفاده کن." if user_lang == 'fa' else "✅ **Logged out successfully!**\n\nUse /start for normal bot access."
        await query.edit_message_text(
            logout_text,
            parse_mode='Markdown'
        )
    elif query.data == 'admin_back':
        await show_admin_panel(update)
    elif query.data == 'admin_back_main':
        await show_admin_panel(update)
    elif query.data == 'admin_manage_admins':
        await show_admin_manage_panel(update)
    elif query.data == 'admin_broadcast':
        await show_broadcast_panel(update)
    elif query.data == 'admin_settings':
        await show_system_settings_panel(update)
    elif query.data == 'admin_list_admins':
        await show_admin_list(query, user_lang)
    elif query.data == 'admin_backup_db':
        await handle_database_backup(query, user_lang)
    elif query.data == 'admin_view_logs':
        await show_system_logs(query, user_lang)
    else:
        feature_text = f"🚧 **ویژگی به زودی**\n\nویژگی '{query.data}' در حال توسعه است." if user_lang == 'fa' else f"🚧 **Feature Coming Soon**\n\nThe '{query.data}' feature is under development."
        await query.edit_message_text(
            feature_text,
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton(f"🔙 {texts['admin_back']}", callback_data='admin_back')
            ]]),
            parse_mode='Markdown'
        )

async def show_admin_list(query, user_lang: str) -> None:
    """Show list of all admins."""
    texts = TEXTS.get(user_lang, TEXTS['en'])

    # Get admin list from config
    from config import ADMIN_IDS

    admin_list_text = f"👥 **لیست ادمین‌ها:**\n\n" if user_lang == 'fa' else f"👥 **Admin List:**\n\n"

    for i, admin_id in enumerate(ADMIN_IDS, 1):
        try:
            # Try to get user info from database
            user_info = db.get_user_info(admin_id)
            if user_info:
                username = user_info.get('username', 'N/A')
                first_name = user_info.get('first_name', 'N/A')
                admin_list_text += f"{i}. **{first_name}** (@{username})\n   ID: `{admin_id}`\n\n"
            else:
                admin_list_text += f"{i}. **Admin {i}**\n   ID: `{admin_id}`\n\n"
        except:
            admin_list_text += f"{i}. **Admin {i}**\n   ID: `{admin_id}`\n\n"

    await query.edit_message_text(
        admin_list_text,
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton(f"🔙 {texts['admin_back']}", callback_data='admin_manage_admins')
        ]]),
        parse_mode='Markdown'
    )

async def handle_database_backup(query, user_lang: str) -> None:
    """Handle database backup."""
    texts = TEXTS.get(user_lang, TEXTS['en'])

    try:
        import shutil
        import datetime

        # Create backup filename with timestamp
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"backup_nullcore_{timestamp}.db"

        # Copy database file
        shutil.copy2("nullcore_bot.db", backup_filename)

        backup_text = f"✅ **بک‌آپ موفقیت‌آمیز!**\n\n📁 فایل: `{backup_filename}`\n📅 تاریخ: {timestamp}" if user_lang == 'fa' else f"✅ **Backup Successful!**\n\n📁 File: `{backup_filename}`\n📅 Date: {timestamp}"

    except Exception as e:
        backup_text = f"❌ **خطا در بک‌آپ!**\n\n🔍 جزئیات: {str(e)}" if user_lang == 'fa' else f"❌ **Backup Failed!**\n\n🔍 Details: {str(e)}"

    await query.edit_message_text(
        backup_text,
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton(f"🔙 {texts['admin_back']}", callback_data='admin_settings')
        ]]),
        parse_mode='Markdown'
    )

async def show_system_logs(query, user_lang: str) -> None:
    """Show system logs."""
    texts = TEXTS.get(user_lang, TEXTS['en'])

    try:
        # Get recent log entries (last 10 lines)
        import os

        log_text = f"📋 **لاگ‌های سیستم:**\n\n" if user_lang == 'fa' else f"📋 **System Logs:**\n\n"

        # Try to read log file if exists
        if os.path.exists("bot.log"):
            with open("bot.log", "r", encoding="utf-8") as f:
                lines = f.readlines()
                recent_lines = lines[-10:] if len(lines) > 10 else lines

                for line in recent_lines:
                    log_text += f"`{line.strip()}`\n"
        else:
            log_text += "هیچ لاگی یافت نشد." if user_lang == 'fa' else "No logs found."

    except Exception as e:
        log_text = f"❌ **خطا در خواندن لاگ‌ها!**\n\n🔍 جزئیات: {str(e)}" if user_lang == 'fa' else f"❌ **Error reading logs!**\n\n🔍 Details: {str(e)}"

    await query.edit_message_text(
        log_text,
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton(f"🔙 {texts['admin_back']}", callback_data='admin_settings')
        ]]),
        parse_mode='Markdown'
    )

async def show_all_users(query, user_lang: str) -> None:
    """Show all users list."""
    texts = TEXTS.get(user_lang, TEXTS['en'])
    users = db.get_all_users()

    if not users:
        no_users_text = "❌ **هیچ کاربری یافت نشد!**" if user_lang == 'fa' else "❌ **No users found!**"
        await query.edit_message_text(
            no_users_text,
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton(f"🔙 {texts['admin_back']}", callback_data='admin_back')
            ]]),
            parse_mode='Markdown'
        )
        return

    title_text = f"👥 **همه کاربران ({len(users)})**\n\n" if user_lang == 'fa' else f"👥 **All Users ({len(users)})**\n\n"
    text = title_text

    for i, user in enumerate(users[:10]):  # Show first 10 users
        username = user['username'] or 'N/A'
        name = user['first_name'] or 'N/A'
        balance = user['wallet_balance']
        premium = "⭐" if user['is_premium'] else ""
        banned = "🚫" if user['is_banned'] else ""

        text += f"{i+1}. `{user['user_id']}` | @{username}\n"
        text += f"   {name} | ${balance:.2f} {premium}{banned}\n\n"

    if len(users) > 10:
        more_text = f"... و {len(users) - 10} کاربر دیگر" if user_lang == 'fa' else f"... and {len(users) - 10} more users"
        text += more_text

    await query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton(f"🔙 {texts['admin_back']}", callback_data='admin_back')
        ]]),
        parse_mode='Markdown'
    )

async def show_statistics(query, user_lang: str) -> None:
    """Show bot statistics."""
    texts = TEXTS.get(user_lang, TEXTS['en'])
    users = db.get_all_users()

    total_users = len(users)
    premium_users = len([u for u in users if u['is_premium']])
    banned_users = len([u for u in users if u['is_banned']])
    total_balance = sum([u['wallet_balance'] for u in users])

    if user_lang == 'fa':
        text = f"📊 **آمار ربات**\n\n" \
               f"👥 کل کاربران: `{total_users}`\n" \
               f"⭐ کاربران پریمیوم: `{premium_users}`\n" \
               f"🚫 کاربران مسدود: `{banned_users}`\n" \
               f"💰 کل موجودی: `${total_balance:.2f}`\n\n" \
               f"📈 کاربران فعال: `{total_users - banned_users}`"
    else:
        text = f"📊 **Bot Statistics**\n\n" \
               f"👥 Total Users: `{total_users}`\n" \
               f"⭐ Premium Users: `{premium_users}`\n" \
               f"🚫 Banned Users: `{banned_users}`\n" \
               f"💰 Total Balance: `${total_balance:.2f}`\n\n" \
               f"📈 Active Users: `{total_users - banned_users}`"

    await query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton(f"🔙 {texts['admin_back']}", callback_data='admin_back')
        ]]),
        parse_mode='Markdown'
    )

async def get_user_language(user_id: int) -> str:
    """Get user's preferred language from database, default to English."""
    user = db.get_user(user_id)
    if user:
        return user['language']
    return 'en'

def main() -> None:
    """Start the bot."""
    # Create the Application
    application = Application.builder().token(BOT_TOKEN).build()

    # Add handlers
    application.add_handler(CommandHandler("start", start))

    # Admin handlers
    application.add_handler(MessageHandler(
        filters.Text([ADMIN_LOGIN_COMMAND]),
        handle_admin_login
    ))
    application.add_handler(MessageHandler(
        filters.Text([ADMIN_LOGOUT_COMMAND]),
        handle_admin_logout
    ))

    # Callback handlers
    application.add_handler(CallbackQueryHandler(language_callback, pattern='^lang_'))
    application.add_handler(CallbackQueryHandler(menu_callback, pattern='^(file_to_link|uploader)$'))
    application.add_handler(CallbackQueryHandler(admin_callback, pattern='^admin_'))

    # Run the bot until the user presses Ctrl-C
    print("🤖 NullCore Bot is starting...")
    print("🔧 Admin Panel integrated")
    print(f"🔑 Admin login command: {ADMIN_LOGIN_COMMAND}")
    print(f"🚪 Admin logout command: {ADMIN_LOGOUT_COMMAND}")
    print("Press Ctrl+C to stop the bot")

    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == '__main__':
    main()
