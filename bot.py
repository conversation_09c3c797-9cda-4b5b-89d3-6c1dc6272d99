import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes

# Enable logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Store user language preferences
user_languages = {}

# Language texts
TEXTS = {
    'en': {
        'welcome': '🌟 Welcome to Glass Bot! 🌟\n\nPlease Set Language:',
        'language_set': '✅ Welcome to Null Core Bot buddy!',
        'choose_language': 'Please choose your language:',
        'main_menu': '✅ I am a professional all-in-one bot with super speed!\n\n☑️ Just choose from the menu below',
        'file_to_link': 'File To Link',
        'uploader': 'Uploader',
        'file_to_link_selected': 'File To Link selected!\n\nThis feature will be available soon...!',
        'uploader_selected': 'Uploader selected!\n\nThis feature will be available soon...!'
    },
    'fa': {
        'welcome': '🌟 به ربات شیشه‌ای خوش آمدید! 🌟\n\nلطفاً زبان را انتخاب کنید:',
        'language_set': '✅ به بات نال کور خوش اومدی رفیق!',
        'choose_language': 'لطفاً زبان خود را انتخاب کنید:',
        'main_menu': '✅ من یک ربات همه کاره هستم اونم به صورت حرفه ای و با سرعت فوق العاده !\n\n☑️ کافیه فقط از منوی زیر انتخاب کنی',
        'file_to_link': 'فایل تو لینک',
        'uploader': 'آپلودر',
        'file_to_link_selected': 'فایل تو لینک انتخاب شد!\n\nاین ویژگی به زودی در دسترس قرار خواهد گرفت...!',
        'uploader_selected': 'آپلودر انتخاب شد!\n\nاین ویژگی به زودی در دسترس قرار خواهد گرفت...!'
    },
    'ru': {
        'welcome': '🌟 Добро пожаловать в Glass Bot! 🌟\n\nПожалуйста, выберите язык:',
        'language_set': '✅ Добро пожаловать в Null Core Bot, друг!',
        'choose_language': 'Пожалуйста, выберите ваш язык:',
        'main_menu': '✅ Я профессиональный многофункциональный бот с супер скоростью!\n\n☑️ Просто выберите из меню ниже',
        'file_to_link': 'Файл в ссылку',
        'uploader': 'Загрузчик',
        'file_to_link_selected': 'Файл в ссылку выбран!\n\nЭта функция скоро будет доступна...!',
        'uploader_selected': 'Загрузчик выбран!\n\nЭта функция скоро будет доступна...!'
    },
    'de': {
        'welcome': '🌟 Willkommen bei Glass Bot! 🌟\n\nBitte wählen Sie eine Sprache:',
        'language_set': '✅ Willkommen bei Null Core Bot, Kumpel!',
        'choose_language': 'Bitte wählen Sie Ihre Sprache:',
        'main_menu': '✅ Ich bin ein professioneller Allzweck-Bot mit super Geschwindigkeit!\n\n☑️ Wählen Sie einfach aus dem Menü unten',
        'file_to_link': 'Datei zu Link',
        'uploader': 'Uploader',
        'file_to_link_selected': 'Datei zu Link ausgewählt!\n\nDiese Funktion wird bald verfügbar sein...!',
        'uploader_selected': 'Uploader ausgewählt!\n\nDiese Funktion wird bald verfügbar sein...!'
    },
    'fr': {
        'welcome': '🌟 Bienvenue sur Glass Bot! 🌟\n\nVeuillez choisir une langue:',
        'language_set': '✅ Bienvenue sur Null Core Bot, mon ami!',
        'choose_language': 'Veuillez choisir votre langue:',
        'main_menu': '✅ Je suis un bot professionnel polyvalent avec une super vitesse!\n\n☑️ Choisissez simplement dans le menu ci-dessous',
        'file_to_link': 'Fichier vers lien',
        'uploader': 'Téléchargeur',
        'file_to_link_selected': 'Fichier vers lien sélectionné!\n\nCette fonctionnalité sera bientôt disponible...!',
        'uploader_selected': 'Téléchargeur sélectionné!\n\nCette fonctionnalité sera bientôt disponible...!'
    },
    'es': {
        'welcome': '🌟 ¡Bienvenido a Glass Bot! 🌟\n\nPor favor selecciona un idioma:',
        'language_set': '✅ ¡Bienvenido a Null Core Bot, amigo!',
        'choose_language': 'Por favor elige tu idioma:',
        'main_menu': '✅ ¡Soy un bot profesional multipropósito con súper velocidad!\n\n☑️ Solo elige del menú de abajo',
        'file_to_link': 'Archivo a enlace',
        'uploader': 'Subidor',
        'file_to_link_selected': '¡Archivo a enlace seleccionado!\n\n¡Esta función estará disponible pronto...!',
        'uploader_selected': '¡Subidor seleccionado!\n\n¡Esta función estará disponible pronto...!'
    },
    'tr': {
        'welcome': '🌟 Glass Bot\'a hoş geldiniz! 🌟\n\nLütfen bir dil seçin:',
        'language_set': '✅ Null Core Bot\'a hoş geldin dostum!',
        'choose_language': 'Lütfen dilinizi seçin:',
        'main_menu': '✅ Ben süper hızlı profesyonel çok amaçlı bir botum!\n\n☑️ Sadece aşağıdaki menüden seçin',
        'file_to_link': 'Dosya bağlantısı',
        'uploader': 'Yükleyici',
        'file_to_link_selected': 'Dosya bağlantısı seçildi!\n\nBu özellik yakında kullanılabilir olacak...!',
        'uploader_selected': 'Yükleyici seçildi!\n\nBu özellik yakında kullanılabilir olacak...!'
    },
    'zh': {
        'welcome': '🌟 欢迎使用Glass Bot! 🌟\n\n请选择语言:',
        'language_set': '✅ 欢迎使用Null Core Bot，朋友!',
        'choose_language': '请选择您的语言:',
        'main_menu': '✅ 我是一个专业的多功能超高速机器人!\n\n☑️ 只需从下面的菜单中选择',
        'file_to_link': '文件转链接',
        'uploader': '上传器',
        'file_to_link_selected': '文件转链接已选择!\n\n此功能即将推出...!',
        'uploader_selected': '上传器已选择!\n\n此功能即将推出...!'
    }
}

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Send a message when the command /start is issued."""
    user = update.effective_user
    username = user.first_name if user.first_name else user.username if user.username else "Friend"

    # Create glass-style inline keyboard with language options
    keyboard = [
        [
            InlineKeyboardButton("🇮🇷 Persian", callback_data='lang_fa'),
            InlineKeyboardButton("🇺🇸 English", callback_data='lang_en')
        ],
        [
            InlineKeyboardButton("🇷🇺 Russia", callback_data='lang_ru'),
            InlineKeyboardButton("🇩🇪 Germany", callback_data='lang_de')
        ],
        [
            InlineKeyboardButton("🇫🇷 French", callback_data='lang_fr'),
            InlineKeyboardButton("🇪🇸 Spanish", callback_data='lang_es')
        ],
        [
            InlineKeyboardButton("🇹🇷 Turkey", callback_data='lang_tr'),
            InlineKeyboardButton("🇨🇳 Chinese", callback_data='lang_zh')
        ]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    # Send welcome message with language selection
    welcome_text = f"✋🏻Hello {username}\n\n🌟 Welcome To NullCore Bot! 🌟\n\nPlease Set Language:"

    await update.message.reply_text(
        welcome_text,
        reply_markup=reply_markup,
        parse_mode='HTML'
    )

async def language_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle language selection callback."""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    user = query.from_user
    username = user.first_name if user.first_name else user.username if user.username else "Friend"
    language = query.data.split('_')[1]  # Extract language code from callback_data

    # Store user's language preference
    user_languages[user_id] = language

    # Create greeting based on language
    greetings = {
        'fa': f"✋🏻سلام {username}\n\n",
        'en': f"✋🏻Hello {username}\n\n",
        'ru': f"✋🏻Привет {username}\n\n",
        'de': f"✋🏻Hallo {username}\n\n",
        'fr': f"✋🏻Salut {username}\n\n",
        'es': f"✋🏻Hola {username}\n\n",
        'tr': f"✋🏻Merhaba {username}\n\n",
        'zh': f"✋🏻你好 {username}\n\n"
    }
    greeting = greetings.get(language, f"✋🏻Hello {username}\n\n")

    # Get appropriate text based on selected language
    welcome_text = greeting + TEXTS[language]['language_set']

    # Edit the message to show confirmation
    await query.edit_message_text(
        text=welcome_text,
        parse_mode='HTML'
    )

    # Send main menu after a short delay
    await show_main_menu(query, language)

    logger.info(f"User {user_id} selected language: {language}")

async def show_main_menu(query, language: str) -> None:
    """Show the main menu with glass-style buttons."""
    # Create main menu keyboard based on language
    keyboard = [
        [
            InlineKeyboardButton(TEXTS[language]['file_to_link'], callback_data='file_to_link'),
            InlineKeyboardButton(TEXTS[language]['uploader'], callback_data='uploader')
        ]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    # Send main menu message
    menu_text = TEXTS[language]['main_menu']

    await query.message.reply_text(
        text=menu_text,
        reply_markup=reply_markup,
        parse_mode='HTML'
    )

async def menu_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle main menu button callbacks."""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    language = await get_user_language(user_id)

    if query.data == 'file_to_link':
        await query.edit_message_text(
            text=f"🔗 {TEXTS[language]['file_to_link_selected']}",
            parse_mode='HTML'
        )
    elif query.data == 'uploader':
        await query.edit_message_text(
            text=f"📤 {TEXTS[language]['uploader_selected']}",
            parse_mode='HTML'
        )

async def get_user_language(user_id: int) -> str:
    """Get user's preferred language, default to English."""
    return user_languages.get(user_id, 'en')

def main() -> None:
    """Start the bot."""
    # Replace 'YOUR_BOT_TOKEN' with your actual bot token from @BotFather
    BOT_TOKEN = "YOUR_BOT_TOKEN"
    
    # Create the Application
    application = Application.builder().token(BOT_TOKEN).build()
    
    # Add handlers
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CallbackQueryHandler(language_callback, pattern='^lang_'))
    application.add_handler(CallbackQueryHandler(menu_callback, pattern='^(file_to_link|uploader)$'))
    
    # Run the bot until the user presses Ctrl-C
    print("🤖 Glass Bot is starting...")
    print("Press Ctrl+C to stop the bot")
    
    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == '__main__':
    main()
