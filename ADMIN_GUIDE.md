# 🔧 Admin Panel Guide

## راه‌اندازی ادمین پنل

### 1️⃣ تنظیم User ID ادمین

اول باید User ID تلگرام خودت رو پیدا کنی:

1. به [@userinfobot](https://t.me/userinfobot) پیام بده
2. User ID رو کپی کن
3. فایل `config.py` رو باز کن
4. User ID رو جایگزین کن:

```python
ADMIN_IDS = [
    YOUR_USER_ID_HERE,  # جایگزین کن
    # می‌تونی چندتا ادمین اضافه کنی
]
```

### 2️⃣ دستورات ادمین

#### 🔑 ورود به ادمین پنل:
```
admin!login
```

#### 🚪 خروج از ادمین پنل:
```
admin!logout
```

#### 🏠 دسترسی به پنل:
```
/start
```
(بعد از لاگین، دستور /start پنل ادمین رو نشون می‌ده)

## 🎛️ قابلیت‌های ادمین پنل

### 👥 All Users
- نمایش لیست همه کاربران
- نمایش User ID، نام کاربری، نام
- نمایش موجودی کیف پول
- نمایش وضعیت پریمیوم (⭐)
- نمایش وضعیت مسدودی (🚫)

### 👤 User Info
- نمایش اطلاعات کامل یک کاربر
- تاریخ عضویت و آخرین فعالیت
- تاریخچه تراکنش‌ها

### 💰 Add Money
- اضافه کردن پول به کیف پول کاربر
- ثبت تراکنش در تاریخچه
- امکان اضافه کردن توضیحات

### 💸 Remove Money
- کم کردن پول از کیف پول کاربر
- ثبت تراکنش منفی
- امکان اضافه کردن توضیحات

### ⭐ Set Premium
- فعال/غیرفعال کردن وضعیت پریمیوم
- تنظیم دسترسی‌های ویژه

### 🚫 Ban User
- مسدود کردن کاربر
- جلوگیری از استفاده از ربات

### 📊 Statistics
- تعداد کل کاربران
- تعداد کاربران پریمیوم
- تعداد کاربران مسدود شده
- مجموع موجودی کیف پول‌ها
- تعداد کاربران فعال

### 🔍 Search User
- جستجو کاربر با نام کاربری
- جستجو کاربر با نام
- جستجو کاربر با User ID

## 🔄 جریان کار ادمین

### ورود به سیستم:
1. پیام `admin!login` بفرست
2. اگر ادمین باشی، پیام تایید می‌گیری
3. دستور `/start` بزن
4. پنل ادمین نمایش داده می‌شه

### کار با کاربران:
1. از منوی ادمین گزینه مورد نظر رو انتخاب کن
2. اطلاعات مورد نیاز رو وارد کن
3. تغییرات اعمال می‌شه

### خروج از سیستم:
1. از پنل ادمین گزینه "🚪 Logout" رو بزن
2. یا پیام `admin!logout` بفرست
3. دستور `/start` دوباره حالت عادی رو نشون می‌ده

## 🛡️ امنیت

### محافظت از دسترسی:
- فقط User ID های تعریف شده در `ADMIN_IDS` دسترسی دارن
- سیستم session برای لاگین/لاگ‌اوت
- هر بار restart ربات، همه ادمین‌ها لاگ‌اوت می‌شن

### نکات امنیتی:
- User ID ادمین رو محرمانه نگه دار
- به صورت منظم از دیتابیس پشتیبان بگیر
- فایل `config.py` رو در گیت محرمانه نگه دار

## 🔧 تنظیمات پیشرفته

### اضافه کردن ادمین جدید:
```python
ADMIN_IDS = [
    123456789,  # ادمین اول
    987654321,  # ادمین دوم
    # ادمین‌های بیشتر...
]
```

### تغییر دستورات ادمین:
```python
ADMIN_LOGIN_COMMAND = "admin!login"   # تغییر دستور ورود
ADMIN_LOGOUT_COMMAND = "admin!logout" # تغییر دستور خروج
```

## 🚨 عیب‌یابی

### مشکلات رایج:

#### ❌ "Access denied!"
- User ID رو در `config.py` چک کن
- مطمئن شو User ID درست وارد شده

#### ❌ "Please login first"
- اول `admin!login` بفرست
- بعد `/start` بزن

#### ❌ پنل ادمین نمایش داده نمی‌شه
- مطمئن شو لاگین کردی
- ربات رو restart کن

### لاگ‌ها:
- همه فعالیت‌های ادمین در کنسول لاگ می‌شن
- برای دیباگ از لاگ‌ها استفاده کن

## 📱 مثال استفاده

```
1. پیام بفرست: admin!login
2. پاسخ: ✅ Admin logged in successfully!
3. پیام بفرست: /start
4. پنل ادمین نمایش داده می‌شه
5. گزینه "👥 All Users" رو بزن
6. لیست کاربران نمایش داده می‌شه
7. برای خروج: "🚪 Logout" یا admin!logout
```

## 🎯 نکات مهم

- همیشه بعد از تغییرات مهم، از دیتابیس پشتیبان بگیر
- قبل از حذف کاربر، مطمئن شو
- موجودی کیف پول‌ها رو به صورت منظم چک کن
- از قابلیت Statistics برای مانیتورینگ استفاده کن

ادمین پنل آماده استفاده است! 🚀
