#!/usr/bin/env python3
"""
Admin Panel for Telegram Bot Management
Simple command-line interface for managing users, wallets, and bot data
"""

import sys
from database import db
from datetime import datetime

class AdminPanel:
    def __init__(self):
        self.commands = {
            '1': self.list_users,
            '2': self.user_info,
            '3': self.add_money,
            '4': self.remove_money,
            '5': self.set_premium,
            '6': self.ban_user,
            '7': self.unban_user,
            '8': self.delete_user,
            '9': self.user_transactions,
            '10': self.search_user,
            '0': self.exit_panel
        }
    
    def show_menu(self):
        """Display main menu."""
        print("\n" + "="*50)
        print("🤖 NULLCORE BOT - ADMIN PANEL")
        print("="*50)
        print("1️⃣  List All Users")
        print("2️⃣  User Information")
        print("3️⃣  Add Money to Wallet")
        print("4️⃣  Remove Money from Wallet")
        print("5️⃣  Set Premium Status")
        print("6️⃣  Ban User")
        print("7️⃣  Unban User")
        print("8️⃣  Delete User")
        print("9️⃣  User Transactions")
        print("🔍 Search User (10)")
        print("0️⃣  Exit")
        print("="*50)
    
    def list_users(self):
        """List all users."""
        users = db.get_all_users()
        if not users:
            print("❌ No users found!")
            return
        
        print(f"\n📊 Total Users: {len(users)}")
        print("-" * 80)
        print(f"{'ID':<12} {'Username':<15} {'Name':<15} {'Balance':<10} {'Premium':<8} {'Banned':<7}")
        print("-" * 80)
        
        for user in users:
            username = user['username'] or 'N/A'
            name = user['first_name'] or 'N/A'
            balance = f"${user['wallet_balance']:.2f}"
            premium = "✅" if user['is_premium'] else "❌"
            banned = "🚫" if user['is_banned'] else "✅"
            
            print(f"{user['user_id']:<12} {username:<15} {name:<15} {balance:<10} {premium:<8} {banned:<7}")
    
    def user_info(self):
        """Get detailed user information."""
        user_id = self.get_user_id()
        if not user_id:
            return
        
        user = db.get_user(user_id)
        if not user:
            print("❌ User not found!")
            return
        
        print(f"\n👤 User Information:")
        print("-" * 40)
        print(f"🆔 User ID: {user['user_id']}")
        print(f"👤 Username: {user['username'] or 'N/A'}")
        print(f"📝 First Name: {user['first_name'] or 'N/A'}")
        print(f"📝 Last Name: {user['last_name'] or 'N/A'}")
        print(f"🌍 Language: {user['language']}")
        print(f"💰 Wallet Balance: ${user['wallet_balance']:.2f}")
        print(f"⭐ Premium: {'Yes' if user['is_premium'] else 'No'}")
        print(f"🚫 Banned: {'Yes' if user['is_banned'] else 'No'}")
        print(f"📅 Join Date: {user['join_date']}")
        print(f"⏰ Last Activity: {user['last_activity']}")
    
    def add_money(self):
        """Add money to user's wallet."""
        user_id = self.get_user_id()
        if not user_id:
            return
        
        try:
            amount = float(input("💰 Enter amount to add: $"))
            description = input("📝 Description (optional): ") or "Admin credit"
            
            if db.update_wallet_balance(user_id, amount, "admin_credit", description):
                print(f"✅ Successfully added ${amount:.2f} to user {user_id}")
            else:
                print("❌ Failed to add money!")
        except ValueError:
            print("❌ Invalid amount!")
    
    def remove_money(self):
        """Remove money from user's wallet."""
        user_id = self.get_user_id()
        if not user_id:
            return
        
        try:
            amount = float(input("💸 Enter amount to remove: $"))
            description = input("📝 Description (optional): ") or "Admin debit"
            
            if db.update_wallet_balance(user_id, -amount, "admin_debit", description):
                print(f"✅ Successfully removed ${amount:.2f} from user {user_id}")
            else:
                print("❌ Failed to remove money!")
        except ValueError:
            print("❌ Invalid amount!")
    
    def set_premium(self):
        """Set user premium status."""
        user_id = self.get_user_id()
        if not user_id:
            return
        
        status = input("⭐ Set premium status (y/n): ").lower().strip()
        is_premium = status in ['y', 'yes', '1', 'true']
        
        if db.set_user_premium(user_id, is_premium):
            status_text = "enabled" if is_premium else "disabled"
            print(f"✅ Premium status {status_text} for user {user_id}")
        else:
            print("❌ Failed to update premium status!")
    
    def ban_user(self):
        """Ban a user."""
        user_id = self.get_user_id()
        if not user_id:
            return
        
        if db.ban_user(user_id, True):
            print(f"✅ User {user_id} has been banned")
        else:
            print("❌ Failed to ban user!")
    
    def unban_user(self):
        """Unban a user."""
        user_id = self.get_user_id()
        if not user_id:
            return
        
        if db.ban_user(user_id, False):
            print(f"✅ User {user_id} has been unbanned")
        else:
            print("❌ Failed to unban user!")
    
    def delete_user(self):
        """Delete a user completely."""
        user_id = self.get_user_id()
        if not user_id:
            return
        
        confirm = input(f"⚠️  Are you sure you want to DELETE user {user_id}? (type 'DELETE'): ")
        if confirm != 'DELETE':
            print("❌ Deletion cancelled!")
            return
        
        if db.delete_user(user_id):
            print(f"✅ User {user_id} has been deleted completely")
        else:
            print("❌ Failed to delete user!")
    
    def user_transactions(self):
        """Show user transaction history."""
        user_id = self.get_user_id()
        if not user_id:
            return
        
        try:
            limit = int(input("📊 Number of transactions to show (default 10): ") or "10")
        except ValueError:
            limit = 10
        
        transactions = db.get_user_transactions(user_id, limit)
        if not transactions:
            print("❌ No transactions found!")
            return
        
        print(f"\n💳 Last {len(transactions)} transactions for user {user_id}:")
        print("-" * 60)
        print(f"{'Amount':<12} {'Type':<15} {'Description':<20} {'Date':<15}")
        print("-" * 60)
        
        for tx in transactions:
            amount = f"${tx['amount']:+.2f}"
            print(f"{amount:<12} {tx['type']:<15} {tx['description']:<20} {tx['timestamp'][:16]}")
    
    def search_user(self):
        """Search for users by username or name."""
        query = input("🔍 Enter username or name to search: ").strip()
        if not query:
            print("❌ Please enter a search term!")
            return
        
        users = db.get_all_users()
        results = []
        
        for user in users:
            if (query.lower() in (user['username'] or '').lower() or 
                query.lower() in (user['first_name'] or '').lower()):
                results.append(user)
        
        if not results:
            print("❌ No users found!")
            return
        
        print(f"\n🔍 Found {len(results)} user(s):")
        print("-" * 60)
        for user in results:
            username = user['username'] or 'N/A'
            name = user['first_name'] or 'N/A'
            print(f"🆔 {user['user_id']} | @{username} | {name} | ${user['wallet_balance']:.2f}")
    
    def get_user_id(self):
        """Get user ID from input."""
        try:
            user_id = int(input("🆔 Enter User ID: "))
            return user_id
        except ValueError:
            print("❌ Invalid User ID!")
            return None
    
    def exit_panel(self):
        """Exit the admin panel."""
        print("👋 Goodbye!")
        sys.exit(0)
    
    def run(self):
        """Run the admin panel."""
        print("🚀 Starting Admin Panel...")
        
        while True:
            try:
                self.show_menu()
                choice = input("\n🎯 Enter your choice: ").strip()
                
                if choice in self.commands:
                    self.commands[choice]()
                else:
                    print("❌ Invalid choice! Please try again.")
                
                input("\n⏸️  Press Enter to continue...")
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")

if __name__ == "__main__":
    admin = AdminPanel()
    admin.run()
